#!/bin/bash

# Test script for navigation bar restoration issue
# Bundle ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
MAIN_ACTIVITY="com.tqhit.battery.one.activity.main.MainActivity"
SPLASH_ACTIVITY="com.tqhit.battery.one.activity.splash.SplashActivity"

echo "=== Navigation Bar Restoration Test ==="
echo "Package: $PACKAGE_NAME"
echo "Testing navigation bar consistency across different launch scenarios"
echo ""

# Function to check if app is running
check_app_running() {
    adb shell "ps | grep $PACKAGE_NAME" > /dev/null 2>&1
    return $?
}

# Function to count visible navigation items via logcat
count_navigation_items() {
    echo "Monitoring navigation setup for 10 seconds..."
    timeout 10s adb logcat -s "MainActivity:D" "DynamicNavigationManager:D" | grep -E "(NAVIGATION_RESTORE|visible menu items|Menu item.*visibility)" | tail -20
}

# Function to force stop app
force_stop_app() {
    echo "Force stopping app..."
    adb shell am force-stop $PACKAGE_NAME
    sleep 2
}

# Function to clear logcat
clear_logcat() {
    adb logcat -c
}

# Test 1: Fresh install/launch via SplashActivity
echo "=== TEST 1: Fresh Launch via SplashActivity ==="
force_stop_app
clear_logcat

echo "Launching app via SplashActivity..."
adb shell am start -n "$PACKAGE_NAME/$SPLASH_ACTIVITY"
sleep 3

echo "Checking navigation setup..."
count_navigation_items
echo ""

# Test 2: Direct MainActivity launch
echo "=== TEST 2: Direct MainActivity Launch ==="
force_stop_app
clear_logcat

echo "Launching MainActivity directly..."
adb shell am start -n "$PACKAGE_NAME/$MAIN_ACTIVITY"
sleep 3

echo "Checking navigation setup..."
count_navigation_items
echo ""

# Test 3: App restoration after force stop
echo "=== TEST 3: App Restoration After Force Stop ==="
echo "First, launch app normally..."
force_stop_app
clear_logcat

adb shell am start -n "$PACKAGE_NAME/$SPLASH_ACTIVITY"
sleep 3

echo "App launched, now force stopping..."
force_stop_app
sleep 2

echo "Restoring app from recent apps (simulating user action)..."
clear_logcat
adb shell am start -n "$PACKAGE_NAME/$MAIN_ACTIVITY" -a android.intent.action.MAIN -c android.intent.category.LAUNCHER
sleep 3

echo "Checking navigation setup after restoration..."
count_navigation_items
echo ""

# Test 4: State restoration with different charging states
echo "=== TEST 4: State Restoration with Charging State Changes ==="
echo "This test requires manual charging state changes during execution"

force_stop_app
clear_logcat

echo "Launching app..."
adb shell am start -n "$PACKAGE_NAME/$SPLASH_ACTIVITY"
sleep 3

echo "Please change charging state (plug/unplug charger) and then force stop the app"
echo "Press Enter when ready to continue..."
read

force_stop_app
sleep 2

echo "Restoring app..."
clear_logcat
adb shell am start -n "$PACKAGE_NAME/$MAIN_ACTIVITY"
sleep 3

echo "Checking navigation setup after charging state change..."
count_navigation_items
echo ""

# Test 5: Continuous monitoring
echo "=== TEST 5: Continuous Monitoring ==="
echo "Monitoring navigation-related logs for 30 seconds..."
echo "You can interact with the app during this time"
echo ""

clear_logcat
timeout 30s adb logcat -s "MainActivity:D" "DynamicNavigationManager:D" "NavigationState:D" | grep -E "(NAVIGATION_RESTORE|visible|Menu item|selectedItemId|charging state)"

echo ""
echo "=== Test Complete ==="
echo "Review the logs above to verify:"
echo "1. Navigation bar shows exactly 4 items in all scenarios"
echo "2. No 5-item navigation bar appears"
echo "3. State restoration works correctly"
echo "4. Charging state changes are handled properly"
